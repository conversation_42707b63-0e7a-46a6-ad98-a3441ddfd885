import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import SearchForm from "./SearchForm";
import heroImage from "@/assets/9d8726ef-7525-46ab-9aa0-96b450e01622.jpg";
import { Search } from "lucide-react";

const HeroSection = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <section className="relative min-h-screen bg-white overflow-hidden">
      {/* Main Content Container */}
      <div className="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-screen flex items-center">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center w-full">
          {/* Left Side - Image */}
          <div className="relative order-2 lg:order-1">
            <div className="relative rounded-3xl overflow-hidden shadow-2xl">
              <img
                src={heroImage}
                alt="Modern luxury apartment interior with comfortable living space"
                className="w-full h-[500px] lg:h-[600px] object-cover"
              />
              {/* Subtle overlay for better text readability if needed */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent" />
            </div>

            {/* Floating decorative elements */}
            <div className="absolute -top-6 -right-6 w-24 h-24 bg-blue-100 rounded-full opacity-60 animate-pulse"></div>
            <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-teal-100 rounded-full opacity-40 animate-pulse delay-1000"></div>
          </div>

          {/* Right Side - Content */}
          <div
            className={`order-1 lg:order-2 ${
              isRTL ? "text-right" : "text-left"
            }`}
          >
            {/* Main Heading */}
            <div className="mb-8">
              <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 mb-4 leading-tight">
                <span className="block text-gray-800">{t("hero.title")}</span>
                <span className="block text-teal-600">
                  {t("hero.subtitle")}
                </span>
              </h1>
              <p className="text-lg lg:text-xl text-gray-600 max-w-lg leading-relaxed">
                {t("hero.description")}
              </p>
            </div>

            {/* Search Form - Compact Version */}
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-blue-100 rounded-xl">
                  <Search className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 text-lg">
                    {isRTL
                      ? "ابحث عن مكان إقامتك المثالي"
                      : "Find Your Perfect Stay"}
                  </h3>
                  <p className="text-gray-500 text-sm">
                    {isRTL
                      ? "اكتشف أفضل العقارات في موقعك المفضل"
                      : "Discover the best properties in your preferred location"}
                  </p>
                </div>
              </div>

              {/* Simplified Search Inputs */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 mb-4">
                <div className="relative">
                  <input
                    type="text"
                    placeholder={
                      isRTL ? "أين تريد الإقامة؟" : "Where do you want to stay?"
                    }
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-sm"
                  />
                </div>
                <div className="relative">
                  <input
                    type="text"
                    placeholder={isRTL ? "تاريخ الوصول" : "Check-in date"}
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-sm"
                  />
                </div>
                <div className="relative">
                  <input
                    type="text"
                    placeholder={isRTL ? "عدد النزلاء" : "Guests"}
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-sm"
                  />
                </div>
              </div>

              <button className="w-full bg-gradient-to-r from-blue-600 to-teal-600 text-white py-3 px-6 rounded-xl font-semibold hover:from-blue-700 hover:to-teal-700 transition-all duration-300 shadow-lg hover:shadow-xl">
                {t("search.search")}
              </button>
            </div>

            {/* Trust Indicators */}
            <div className="mt-8 flex items-center gap-8 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>{isRTL ? "تقييم 4.9" : "4.9 Rating"}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>{isRTL ? "+50 ألف ضيف سعيد" : "50K+ Happy Guests"}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span>{isRTL ? "حائز على جوائز" : "Award Winning"}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
